//
//  NotificationManager.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/13.
//  通知管理器 - 处理系统通知的发送和权限管理
//

import Foundation
import UserNotifications
import SwiftUI

/// 通知管理器
class NotificationManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    /// 是否已授权通知权限
    @Published var isAuthorized: Bool = false
    
    /// 权限状态
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    // MARK: - Private Properties
    
    /// 通知中心
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // MARK: - Constants
    
    /// 通知标识符
    private enum NotificationIdentifier {
        static let breakStart = "zen.tomato.break.start"
        static let breakEnd = "zen.tomato.break.end"
        static let workStart = "zen.tomato.work.start"
    }
    
    /// 通知动作标识符
    private enum NotificationAction {
        static let skip = "zen.tomato.action.skip"
        static let startNow = "zen.tomato.action.start"
    }
    
    /// 通知类别标识符
    private enum NotificationCategory {
        static let timerAlert = "zen.tomato.category.timer"
    }
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        notificationCenter.delegate = self
        checkAuthorizationStatus()
        setupNotificationCategories()
    }
    
    // MARK: - Public Methods
    
    /// 请求通知权限
    func requestPermission() {
        notificationCenter.requestAuthorization(options: [.alert, .sound, .badge]) { [weak self] granted, error in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                self?.checkAuthorizationStatus()

                if let error = error {
                    print("\(NSLocalizedString("error.notification.permission_failed", comment: "")): \(error)")
                } else if granted {
                    // 权限获取成功，发送测试通知
                    self?.sendWelcomeNotification()
                }
            }
        }
    }

    /// 仅在需要时请求通知权限（首次使用）
    func requestPermissionIfNeeded() {
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.authorizationStatus = settings.authorizationStatus
                self?.isAuthorized = settings.authorizationStatus == .authorized

                // 只在未决定状态时请求权限
                if settings.authorizationStatus == .notDetermined {
                    self?.requestPermission()
                }
            }
        }
    }

    /// 发送欢迎通知（首次授权后）
    private func sendWelcomeNotification() {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.welcome.title", comment: "")
        content.body = NSLocalizedString("notification.welcome.body", comment: "")
        content.sound = .default

        sendNotification(content: content, identifier: "zen.tomato.welcome")
    }

    /// 发送测试通知（用于调试）
    func sendTestNotification() {
        guard isAuthorized else {
            print("通知权限未授权，无法发送测试通知")
            return
        }

        let content = UNMutableNotificationContent()
        content.title = "测试通知"
        content.body = "这是一个测试通知，用于验证通知功能是否正常工作"
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert

        sendNotification(content: content, identifier: "zen.tomato.test")
    }
    
    /// 发送休息开始通知
    func sendBreakStartNotification(duration: TimeInterval) {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.break_start.title", comment: "")
        content.body = String(format: NSLocalizedString("notification.break_start.body", comment: ""), Int(duration / 60))
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "breakStart", "duration": duration]

        // 添加动作按钮
        content.interruptionLevel = .timeSensitive

        sendNotification(content: content, identifier: NotificationIdentifier.breakStart)
    }
    
    /// 发送休息结束通知
    func sendBreakEndNotification() {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.break_end.title", comment: "")
        content.body = NSLocalizedString("notification.break_end.body", comment: "")
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "breakEnd"]

        // 添加动作按钮
        content.interruptionLevel = .timeSensitive

        sendNotification(content: content, identifier: NotificationIdentifier.breakEnd)
    }
    
    /// 发送工作开始通知
    func sendWorkStartNotification() {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.work_start.title", comment: "")
        content.body = NSLocalizedString("notification.work_start.body", comment: "")
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "workStart"]

        sendNotification(content: content, identifier: NotificationIdentifier.workStart)
    }
    
    /// 移除所有待发送的通知
    func removeAllPendingNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    /// 移除所有已发送的通知
    func removeAllDeliveredNotifications() {
        notificationCenter.removeAllDeliveredNotifications()
    }

    /// 清理旧通知（保留最近的5条）
    func cleanupOldNotifications() {
        notificationCenter.getDeliveredNotifications { notifications in
            let sortedNotifications = notifications.sorted {
                $0.date > $1.date
            }

            // 保留最近的5条通知，删除其余的
            if sortedNotifications.count > 5 {
                let notificationsToRemove = Array(sortedNotifications.dropFirst(5))
                let identifiersToRemove = notificationsToRemove.map { $0.request.identifier }

                DispatchQueue.main.async { [weak self] in
                    self?.notificationCenter.removeDeliveredNotifications(withIdentifiers: identifiersToRemove)
                }
            }
        }
    }
    
    /// 打开系统设置
    func openSystemSettings() {
        // macOS Ventura (13.0) 及更高版本使用新的系统设置应用
        if #available(macOS 13.0, *) {
            if let url = URL(string: "x-apple.systempreferences:com.apple.Notifications-Settings.extension") {
                NSWorkspace.shared.open(url)
            }
        } else {
            // macOS Monterey 及更早版本使用旧的系统偏好设置
            if let url = URL(string: "x-apple.systempreferences:com.apple.preference.notifications") {
                NSWorkspace.shared.open(url)
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 检查授权状态
    private func checkAuthorizationStatus() {
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.authorizationStatus = settings.authorizationStatus
                self?.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// 设置通知类别和动作
    private func setupNotificationCategories() {
        // 创建跳过动作
        let skipAction = UNNotificationAction(
            identifier: NotificationAction.skip,
            title: NSLocalizedString("notification.action.skip", comment: ""),
            options: []
        )

        // 创建立即开始动作
        let startAction = UNNotificationAction(
            identifier: NotificationAction.startNow,
            title: NSLocalizedString("notification.action.start_now", comment: ""),
            options: [.foreground]
        )
        
        // 创建计时器提醒类别
        let timerCategory = UNNotificationCategory(
            identifier: NotificationCategory.timerAlert,
            actions: [startAction, skipAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        // 注册类别
        notificationCenter.setNotificationCategories([timerCategory])
    }
    
    /// 发送通知
    private func sendNotification(content: UNMutableNotificationContent, identifier: String) {
        guard isAuthorized else {
            print(NSLocalizedString("error.notification.unauthorized", comment: ""))
            return
        }

        // 设置通知保留在通知中心
        content.threadIdentifier = "ZenTomato.Timer"

        // 创建触发器（立即发送）
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)

        // 创建请求，使用时间戳确保唯一性
        let uniqueIdentifier = "\(identifier).\(Date().timeIntervalSince1970)"
        let request = UNNotificationRequest(
            identifier: uniqueIdentifier,
            content: content,
            trigger: trigger
        )

        // 添加通知请求
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                print("\(NSLocalizedString("error.notification.send_failed", comment: "")): \(error)")
            } else {
                print("通知发送成功: \(content.title)")
                // 清理旧通知
                self?.cleanupOldNotifications()
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {
    
    /// 处理前台通知展示
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 即使应用在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    /// 处理通知响应
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case NotificationAction.skip:
            // 发送跳过休息的通知
            NotificationCenter.default.post(
                name: .skipBreakRequested,
                object: nil,
                userInfo: userInfo
            )
            
        case NotificationAction.startNow:
            // 发送立即开始的通知
            NotificationCenter.default.post(
                name: .startWorkRequested,
                object: nil,
                userInfo: userInfo
            )
            
        case UNNotificationDefaultActionIdentifier:
            // 用户点击了通知本身
            NotificationCenter.default.post(
                name: .notificationTapped,
                object: nil,
                userInfo: userInfo
            )
            
        default:
            break
        }
        
        completionHandler()
    }
}

// MARK: - Notification Names Extension

extension Notification.Name {
    static let skipBreakRequested = Notification.Name("ZenTomato.skipBreakRequested")
    static let startWorkRequested = Notification.Name("ZenTomato.startWorkRequested")
    static let notificationTapped = Notification.Name("ZenTomato.notificationTapped")
}

// MARK: - Preview Helper

extension NotificationManager {
    /// 创建预览用的通知管理器
    static var preview: NotificationManager {
        return NotificationManager()
    }
}

// MARK: - 权限状态显示扩展

extension UNAuthorizationStatus {
    var displayName: String {
        switch self {
        case .notDetermined:
            return NSLocalizedString("status.not_determined", comment: "")
        case .denied:
            return NSLocalizedString("status.denied", comment: "")
        case .authorized:
            return NSLocalizedString("status.authorized", comment: "")
        case .provisional:
            return NSLocalizedString("status.provisional", comment: "")
        case .ephemeral:
            return NSLocalizedString("status.ephemeral", comment: "")
        @unknown default:
            return NSLocalizedString("status.unknown", comment: "")
        }
    }
    
    var icon: String {
        switch self {
        case .notDetermined:
            return "questionmark.circle"
        case .denied:
            return "xmark.circle"
        case .authorized:
            return "checkmark.circle"
        case .provisional, .ephemeral:
            return "clock.circle"
        @unknown default:
            return "exclamationmark.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .notDetermined:
            return .gray
        case .denied:
            return .red
        case .authorized:
            return .green
        case .provisional, .ephemeral:
            return .orange
        @unknown default:
            return .gray
        }
    }
}