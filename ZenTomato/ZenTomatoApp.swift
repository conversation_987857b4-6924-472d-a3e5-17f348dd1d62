//
//  ZenTomatoApp.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/13.
//  主应用入口 - 菜单栏应用
//

import SwiftUI
import AppKit

@main
struct ZenTomatoApp: App {
    // 使用 NSApplicationDelegateAdaptor 来管理应用生命周期
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    var body: some Scene {
        // 创建一个空的场景，因为我们使用菜单栏
        Settings {
            EmptyView()
        }
    }
}

// MARK: - App Delegate
class AppDelegate: NSObject, NSApplicationDelegate {
    
    /// 菜单栏管理器
    var menuBarManager: MenuBarManager?
    
    /// 计时引擎
    let timerEngine = TimerEngine()
    
    /// 音频播放器
    let audioPlayer = AudioPlayer()
    
    /// 通知管理器
    let notificationManager = NotificationManager()
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        // 隐藏主窗口和 Dock 图标
        NSApp.setActivationPolicy(.accessory)
        
        // 初始化菜单栏
        menuBarManager = MenuBarManager(
            timerEngine: timerEngine,
            audioPlayer: audioPlayer,
            notificationManager: notificationManager
        )
        menuBarManager?.setupMenuBar()
        
        // 检查并请求通知权限（仅在首次使用时）
        notificationManager.requestPermissionIfNeeded()
        
        // 设置观察者
        setupObservers()
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        // 清理资源
        timerEngine.stop()
        audioPlayer.stopAllSounds()
    }
    
    /// 设置通知观察者
    private func setupObservers() {
        // 监听计时器阶段开始
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhaseStarted(_:)),
            name: .timerPhaseStarted,
            object: nil
        )
        
        // 监听计时器阶段完成
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhaseCompleted(_:)),
            name: .timerPhaseCompleted,
            object: nil
        )
        
        // 监听通知快速操作 - 跳过休息
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSkipBreakRequested(_:)),
            name: .skipBreakRequested,
            object: nil
        )
        
        // 监听通知快速操作 - 立即开始工作
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStartWorkRequested(_:)),
            name: .startWorkRequested,
            object: nil
        )
        
        // 监听通知点击事件
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNotificationTapped(_:)),
            name: .notificationTapped,
            object: nil
        )
    }
    
    @objc private func handlePhaseStarted(_ notification: Notification) {
        guard let phase = notification.userInfo?["phase"] as? TimerPhase else { return }

        // 播放开始音效
        if phase == .work {
            audioPlayer.playWindupSound()
            audioPlayer.startTickingSound()
            // 发送工作开始通知（仅在从休息切换到工作时）
            if timerEngine.completedCycles > 0 {
                notificationManager.sendWorkStartNotification()
            }
        } else {
            audioPlayer.stopTickingSound()
        }
    }
    
    @objc private func handlePhaseCompleted(_ notification: Notification) {
        guard let phase = notification.userInfo?["phase"] as? TimerPhase else { return }
        
        // 播放结束音效
        audioPlayer.playDingSound()
        audioPlayer.stopTickingSound()
        
        // 发送通知
        switch phase {
        case .work:
            let nextPhase = timerEngine.completedCycles % timerEngine.configuration.cyclesBeforeLongBreak == 0 
                ? TimerPhase.longBreak 
                : TimerPhase.shortBreak
            notificationManager.sendBreakStartNotification(
                duration: nextPhase == .longBreak 
                    ? timerEngine.configuration.longBreakDuration 
                    : timerEngine.configuration.shortBreakDuration
            )
        case .shortBreak, .longBreak:
            notificationManager.sendBreakEndNotification()
        }
    }
    
    /// 处理跳过休息请求
    @objc private func handleSkipBreakRequested(_ notification: Notification) {
        print("用户通过通知请求跳过休息")
        
        // 如果当前是休息阶段，则跳过
        if timerEngine.currentPhase == .shortBreak || timerEngine.currentPhase == .longBreak {
            timerEngine.skip()
        }
        
        // 可选：显示菜单栏面板
        menuBarManager?.showPopover()
    }
    
    /// 处理立即开始工作请求
    @objc private func handleStartWorkRequested(_ notification: Notification) {
        print("用户通过通知请求立即开始工作")
        
        // 如果当前不在工作阶段，切换到工作并开始
        if timerEngine.currentPhase != .work {
            timerEngine.currentPhase = .work
        }
        
        // 开始计时
        timerEngine.start()
        
        // 可选：显示菜单栏面板
        menuBarManager?.showPopover()
    }
    
    /// 处理通知点击事件
    @objc private func handleNotificationTapped(_ notification: Notification) {
        print("用户点击了系统通知")
        
        // 显示菜单栏面板，让用户查看当前状态
        menuBarManager?.showPopover()
        
        // 可选：根据当前状态执行默认动作
        if timerEngine.currentState == .idle || timerEngine.currentState == .completed {
            // 如果计时器空闲，默认开始新一轮
            timerEngine.start()
        }
    }
}

// MARK: - Preview Helpers
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        Text("ZenTomato - 菜单栏应用")
            .frame(width: 300, height: 200)
    }
}
